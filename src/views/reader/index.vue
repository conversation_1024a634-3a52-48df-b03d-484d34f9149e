<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-12 10:34:47
 * @LastEditTime: 2025-02-18 14:43:09
 * @FilePath: \dutp-stu-tea-vue\src\views\reader\index.vue
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->

<style lang="scss">
@import './sub/readerTheme.scss';
.__reader__ {
  width: 100vw;
  height: 100vh;
  background-color: var(--pageBackgroundColor);
  .reader-main {
    width: 100%;
    overflow: auto;
    height: calc(100% - 48px);
  }
}
</style>
<template>
  <div class="__reader__"
    :reader-theme="store.theme"
    v-loading="loading"
    rer="reader"
    @contextmenu.prevent
    @copy.prevent>
    <Header
      :messageTag="messageTag"
      ref="headerRef"
      @show-help="showhelpFun" />
    <div class="reader-main">
      <Content
        ref="contentRef"
        :init-chapterId="initedChapterId"
        :init-cataId="initedCataId" />
    </div>

    <el-tour
      v-model="tourOpen"
      @close="handelClose"
      @finish="handelClose">
      <el-tour-step
        description="5种主题色切换"
        :target="headerRef?.themeRef"
        placement="bottom" />

      <el-tour-step
        description="设置版式样式"
        :target="headerRef?.styleRef"
        placement="bottom" />

      <el-tour-step
        description="多种模式阅读"
        :target="headerRef?.readRef"
        placement="bottom" />

      <el-tour-step
        description="AI专属助教随时在线"
        :target="headerRef?.aiRef"
        placement="bottom" />

      <el-tour-step
        description="支持多种翻页模式"
        :target="headerRef?.pageRef"
        placement="bottom" />
      <el-tour-step
        description="划线列表可导出"
        :target="headerRef?.markRef"
        placement="bottom" />

      <el-tour-step
        description="书签列表汇总"
        :target="headerRef?.bookMarkRef"
        placement="bottom" />

      <el-tour-step
        description="多种导航模式阅读"
        :target="contentRef?.menusRef?.menusTabsRef"
        placement="bottom" />

      <el-tour-step
        description="一键展开所有目录"
        :target="contentRef?.menusRef?.menusContentRef.menuStatusRef"
        placement="bottom" />

      <el-tour-step
        description="全方位学习资源"
        :target="contentRef?.toolRef?.toolListRef"
        placement="left" />

      <el-tour-step
        description="响应式阅读区，随时随地创建笔记。"
        placement="left" />
    </el-tour>
  </div>
</template>

<script setup>
// import loadFont01 from "@/views/home/<USER>/PublishersNews/loadFont01.vue";
// 组件中使用
import { useFontStore } from '@/store/modules/fontStore'
import Header from './sub/Header.vue'
import Content from './sub/Content.vue'
import useReader from '@/store/modules/reader'
import { saveBookRead, updateReadTime } from '@/api/book/reader'
import { onMounted, onUnmounted, onBeforeUnmount, watch, onUpdated } from 'vue'
import { getToken } from '@/utils/auth'
import { getMessage } from '@/api/message/message'
import { startRead } from '@/api/book/reader'
import { useRoute, useRouter } from 'vue-router'
import { updateVideoTime } from '@/api/book/reader'
import { listenForJumpRequests } from '@/utils/windowStoreSync'

const fontStore = useFontStore()
let timer = null
const loading = ref(false)
const tourOpen = ref(true)
const messageTag = ref(false)
const headerRef = ref(null)
const contentRef = ref(null)
const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance()
const store = useReader()
const initedChapterId = ref('')
const initedCataId = ref('')
const initedBookId = ref('')
// const reader = ref(null);
function initReaderConfig(bookId, chapterId, catalogId) {
  initedChapterId.value = chapterId
  initedCataId.value = catalogId
  initedBookId.value = bookId
  startRead(bookId).then(res => {
    if (res.code === 200) {
      store.setCommonData(res.data)
    } else {
      store.deauthorizeToTheBook()
      proxy.$message.error(res.msg)
      setTimeout(() => {
        loading.value = false
        router.go(-1)
        // 如果没有权限访问该教材，则跳转到书架页面
        // router.push({ path: "/purchase-history" });
      }, 1000)
    }
  })
}
let startTime = new Date().getTime()
watch(
  () => store.comprehensiveBookData.currentPageIndex,
  (nvalue, ovalue) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      // 切换前计算一下时间
      let endTime = new Date().getTime()
      updateUserReadTime(parseInt((endTime - startTime) / 1000))
      saveUserBookRead()
    }, 200)
  }
)

watch(
  () => route.query,
  (nValue, oValue) => {
    if (nValue.k !== oValue.k) {
      initReaderConfig(nValue.k, nValue.cid, nValue.cataid)
    }
  }
)

onMounted(el => {
  const key = proxy.$route.query.k
  const chapterId = proxy.$route.query.cid
  const catalogId = proxy.$route.query.cataid

  initReaderConfig(key, chapterId, catalogId)
  getMessageList()

  // 监听来自子窗口的跳转请求
  const cleanupJumpListener = listenForJumpRequests(store)

  // 在组件卸载时清理监听器
  onBeforeUnmount(() => {
    cleanupJumpListener()
  })

  const tourOpenStr = localStorage.getItem('tourOpen')
  if (tourOpenStr === 'false') {
    tourOpen.value = false
  } else {
    tourOpen.value = true
  }
  // el.addEventListener("__reader__", (event) => {
  //   event.preventDefault(); // 阻止默认行为，即显示右键菜单
  // });
})

const handelClose = () => {
  tourOpen.value = false
  localStorage.setItem('tourOpen', 'false')
}

const getMessageList = () => {
  const obj = {
    toUserType: 2,
    pageNum: 1,
    pageSize: 10,
    readFlag: 0
  }
  getMessage(obj).then(res => {
    if (res.code === 200) {
      // console.log("res", res);
      if (res.total > 0) {
        messageTag.value = true
      } else {
        messageTag.value = false
      }
    }
  })
}

const showhelpFun = data => {
  // console.log("data", data);
  tourOpen.value = data
}

function saveUserBookRead() {
  const readRate = (store.comprehensiveBookData.currentPageIndex / store.comprehensiveBookData.totalPages) * 100
  if (getToken() && store.comprehensiveBookData.bookId && store.configId && store.chapterId) {
    saveBookRead({
      configId: store.configId,
      bookId: store.comprehensiveBookData.bookId,
      chapterId: store.chapterId,
      pageNumber: store.comprehensiveBookData.currentPageIndex - store.getCumulatedPageCntInPreviousChapters(store.chapterId),
      readRate: Math.round(readRate)
    }).then(res => {
      store.setReadId(res.data)
    })
  }
}
const updateReadTimeTimer = setInterval(() => {
  startTime = new Date().getTime()
  updateUserReadTime(60)
}, 1000 * 60)

function updateUserReadTime(readTime) {
  if (getToken()) {
    if (store.readId) {
      updateReadTime(store.readId, readTime).then(res => {})
    }
  }
}
onUpdated(() => {
  /* let fontArr = ['FZCHYK.TTF', 'FZCYK.TTF'];
  fontStore.loadDutpFonts(fontArr);*/
})
// 组件挂载时加载字体
onMounted(() => {
  // loadFont();
  // let fontArr = ["FZCHYK.TTF", "FZCYK.TTF"];
  // fontStore.loadDutpFonts(fontArr);
})
onUnmounted(() => {
  console.log(store.videoPlayTime)
  clearInterval(updateReadTimeTimer)
  const param = {
    bookId: initedBookId.value,
    fileType: 3,
    chapterId: initedChapterId.value,
    lastTime: store.videoPlayTime
  }
  console.log('什么时候执行的?')
  updateVideoTime(param).then(res => {})
  store.videoPlayTime = 0
})
</script>
